/**
 * @name MeldTeamPlugins
 * @version 0.1.5
 * @description Загрузчик для набора плагинов Meld Team (динамический список с сервера с проверкой).
 * <AUTHOR>
 * @source https://github.com/YourRepo/NotesPlugin
 * @website https://betterdiscord.app/
 */

module.exports = class MeldTeamPlugins {
    constructor() {
        this.pluginInstances = [];
        this.pluginBaseUrl = 'https://meldext.vercel.app/api/discord/plugins';
        this.manifestFileName = 'plugins-manifest.json';
        this.pluginNamespace = 'MeldTeamLoadedPlugins';

        this.discordUserId = null;

        this.availablePlugins = [];
        this.pluginSettings = {};
        this.defaultPluginSettings = {};

        this.manifestLoading = false;
        this.manifestLoaded = false;
        this.manifestError = null;
    }

    getDiscordUserId() {
        if (this.discordUserId) return this.discordUserId;

        try {
            const UserStore = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps('getCurrentUser', 'getUser'));
            const id = UserStore?.getCurrentUser()?.id;
            if (id) {
                this.discordUserId = id;
                console.log('MeldTeamLoader: Discord User ID получен:', this.discordUserId);
                return this.discordUserId;
            } else {
                throw new Error('UserStore или getCurrentUser() не вернули ID.');
            }
        } catch (error) {
            console.error("MeldTeamLoader: Не удалось получить Discord User ID:", error);
            BdApi.showToast("MeldTeam Plugins: Не удалось получить ваш Discord ID!", { type: "error" });
            return null;
        }
    }

    async fetchManifestAndPrepareSettings() {
        if (this.manifestLoading) return;
        if (this.manifestLoaded) return;

        const userId = this.getDiscordUserId();
        if (!userId) {
            this.manifestError = "Не удалось получить Discord ID для авторизации.";
            BdApi.showToast(`MeldTeam Plugins: ${this.manifestError}`, { type: "error", timeout: 7000 });
            return;
        }

        this.manifestLoading = true;
        this.manifestError = null;
        console.log("MeldTeamLoader: Загрузка манифеста плагинов...");
        BdApi.showToast("MeldTeam Plugins: Загрузка списка плагинов...", { type: "info", timeout: 2000 });

        const manifestFetchUrl = `${this.pluginBaseUrl}?file=${this.manifestFileName}&_=${Date.now()}`;

        try {
            const response = await fetch(manifestFetchUrl, {
                cache: "no-store",
                headers: {
                    'X-Discord-User': userId
                }
            });
            if (!response.ok) {
                if (response.status === 403) {
                     throw new Error(`Доступ запрещен (ошибка ${response.status}). Ваш Discord ID (${userId}) не в вайтлисте?`);
                }
                throw new Error(`HTTP ${response.status} при загрузке манифеста`);
            }
            const manifestData = await response.json();

            if (!Array.isArray(manifestData)) {
                 throw new Error("Манифест должен быть массивом JSON");
            }

            this.availablePlugins = manifestData.filter(p => p && p.name && p.file);
            if (this.availablePlugins.length !== manifestData.length) {
                 console.warn("MeldTeamLoader: Некоторые записи в манифесте были некорректны и пропущены.");
            }

            console.log(`MeldTeamLoader: Манифест успешно загружен. Доступно плагинов: ${this.availablePlugins.length}`);

            this.defaultPluginSettings = {};
            for (const pluginInfo of this.availablePlugins) {
                this.defaultPluginSettings[pluginInfo.name] = pluginInfo.defaultEnabled === true;
            }

            const savedSettings = BdApi.loadData(this.getName(), "pluginSettings") || {};

            this.pluginSettings = {};
            for (const pluginInfo of this.availablePlugins) {
                 const name = pluginInfo.name;
                 if (savedSettings.hasOwnProperty(name)) {
                     this.pluginSettings[name] = savedSettings[name];
                } else {
                     this.pluginSettings[name] = this.defaultPluginSettings[name];
                 }
            }

            this.manifestLoaded = true;
             BdApi.showToast(`MeldTeam Plugins: Список плагинов загружен (${this.availablePlugins.length}).`, { type: "success" });


        } catch (error) {
            console.error("MeldTeamLoader: Ошибка загрузки или обработки манифеста:", error);
            this.manifestError = error.message || "Неизвестная ошибка";
            BdApi.showToast(`MeldTeam Plugins: Ошибка загрузки списка плагинов! ${this.manifestError}`, { type: "error", timeout: 7000 });
            this.availablePlugins = [];
            this.manifestLoaded = false;
        } finally {
             this.manifestLoading = false;
        }
    }

    saveSettings() {
        const settingsToSave = {};
        for(const pluginInfo of this.availablePlugins) {
            if (this.pluginSettings.hasOwnProperty(pluginInfo.name)) {
                 settingsToSave[pluginInfo.name] = this.pluginSettings[pluginInfo.name];
            }
        }
        BdApi.saveData(this.getName(), "pluginSettings", settingsToSave);
        console.log("MeldTeamLoader: Настройки сохранены:", settingsToSave);
        this.reloadPlugins();
    }

    getSettingsPanel() {
        // Проверяем наличие ZLibrary и его работоспособность
        const hasWorkingZLibrary = this.checkZLibraryHealth();

        if (!hasWorkingZLibrary) {
            return this.createFallbackSettingsPanel();
        }

        // Пытаемся использовать ZLibrary
        try {
            return this.createZLibrarySettingsPanel();
        } catch (error) {
            console.error("MeldTeamLoader: Ошибка при создании панели с ZLibrary:", error);
            return this.createFallbackSettingsPanel();
        }
    }

    // Проверяем работоспособность ZLibrary
    checkZLibraryHealth() {
        try {
            // Проверяем наличие ZLibrary
            const ZLib = global.ZeresPluginLibrary || window.ZLibrary || global.ZLibrary;
            if (!ZLib) {
                console.log("MeldTeamLoader: ZLibrary не найдена");
                return false;
            }

            // Проверяем наличие Settings
            const Settings = ZLib.Settings || (ZLib.Library && ZLib.Library.Settings);
            if (!Settings) {
                console.log("MeldTeamLoader: Settings не найден в ZLibrary");
                return false;
            }

            // Проверяем наличие необходимых компонентов
            if (!Settings.Switch || !Settings.SettingPanel || !Settings.SettingPanel.build) {
                console.log("MeldTeamLoader: Необходимые компоненты Settings отсутствуют");
                return false;
            }

            // Проверяем наличие React/ReactDOM (основная причина ошибок)
            if (!window.React || !window.ReactDOM) {
                console.log("MeldTeamLoader: React/ReactDOM не найдены");
                return false;
            }

            console.log("MeldTeamLoader: ZLibrary проверка пройдена успешно");
            return true;
        } catch (error) {
            console.error("MeldTeamLoader: Ошибка при проверке ZLibrary:", error);
            return false;
        }
    }

    // Создание панели настроек с использованием ZLibrary
    createZLibrarySettingsPanel() {
        // Безопасное получение Settings из ZLibrary
        let Settings;
        const ZLib = global.ZeresPluginLibrary || window.ZLibrary || global.ZLibrary;
        Settings = ZLib.Settings || (ZLib.Library && ZLib.Library.Settings);

        const settingItems = [];

        const discordId = this.getDiscordUserId();
        const discordIdText = discordId ? `Ваш Discord ID: ${discordId}` : "Не удалось получить Discord ID";
        const discordIdNotice = this.createNoticeElement(discordIdText, discordId ? 'info' : 'error');
        discordIdNotice.style.marginBottom = '20px';
        settingItems.push(discordIdNotice);

        if (this.manifestLoading) {
             settingItems.push(this.createNoticeElement("Загрузка списка плагинов с сервера..."));
        } else if (this.manifestError) {
             settingItems.push(this.createNoticeElement(`Ошибка загрузки списка плагинов: ${this.manifestError}`, 'error'));
             // Кнопка для повторной попытки загрузки манифеста
             try {
                 settingItems.push(
                     new Settings.Button("Попробовать снова", "Загрузить список плагинов с сервера", async () => {
                         await this.fetchManifestAndPrepareSettings();
                         BdApi.showToast("Повторная загрузка завершена. Переоткройте настройки.", { type: "info" });
                     }, { style: Settings.Button.Styles.PRIMARY })
                 );
             } catch (buttonError) {
                 console.error("MeldTeamLoader: Ошибка создания кнопки:", buttonError);
                 // Fallback - простая HTML кнопка
                 const retryButton = document.createElement("button");
                 retryButton.textContent = "Попробовать снова";
                 retryButton.style.cssText = "padding: 8px 16px; margin: 10px 0; background: var(--brand-experiment); color: white; border: none; border-radius: 4px; cursor: pointer;";
                 retryButton.onclick = async () => {
                     await this.fetchManifestAndPrepareSettings();
                     BdApi.showToast("Повторная загрузка завершена. Переоткройте настройки.", { type: "info" });
                 };
                 settingItems.push(retryButton);
             }
        } else if (!this.manifestLoaded || this.availablePlugins.length === 0) {
             settingItems.push(this.createNoticeElement("Список доступных плагинов пуст или не загружен.", 'warn'));
        } else {
            // Манифест загружен, создаем переключатели
            if (!Settings.Switch) {
                console.error("MeldTeamLoader: Settings.Switch не найден в ZLibrary");
                settingItems.push(this.createNoticeElement("Ошибка: Settings.Switch не найден в ZLibrary", 'error'));
            } else {
                for (const pluginInfo of this.availablePlugins) {
                    try {
                        settingItems.push(
                            new Settings.Switch(
                                pluginInfo.name,
                                pluginInfo.description || `Включить/выключить плагин ${pluginInfo.name}`,
                                this.pluginSettings[pluginInfo.name] === true,
                                (checked) => {
                                    this.pluginSettings[pluginInfo.name] = checked;
                                }
                            )
                        );
                    } catch (error) {
                        console.error(`MeldTeamLoader: Ошибка создания Switch для ${pluginInfo.name}:`, error);
                        settingItems.push(this.createNoticeElement(`Ошибка создания переключателя для ${pluginInfo.name}: ${error.message}`, 'error'));
                    }
                }
            }
        }

        // Безопасное создание панели настроек
        if (!Settings.SettingPanel || !Settings.SettingPanel.build) {
            throw new Error("Settings.SettingPanel.build не найден");
        }

        return Settings.SettingPanel.build(
            () => { if (this.manifestLoaded) this.saveSettings(); },
            ...settingItems
        );
    }

    // Создание простой HTML панели настроек без ZLibrary
    createFallbackSettingsPanel() {
        const panel = document.createElement("div");
        panel.style.cssText = "padding: 20px; color: var(--text-normal); font-family: var(--font-primary);";

        // Заголовок
        const title = document.createElement("h2");
        title.textContent = "MeldTeam Plugins - Настройки";
        title.style.cssText = "margin-bottom: 20px; color: var(--header-primary);";
        panel.appendChild(title);

        // Discord ID информация
        const discordId = this.getDiscordUserId();
        const discordIdText = discordId ? `Ваш Discord ID: ${discordId}` : "Не удалось получить Discord ID";
        const discordIdNotice = this.createNoticeElement(discordIdText, discordId ? 'info' : 'error');
        discordIdNotice.style.marginBottom = '20px';
        panel.appendChild(discordIdNotice);

        // Проверяем состояние загрузки манифеста
        if (this.manifestLoading) {
            const notice = this.createNoticeElement("Загрузка списка плагинов с сервера...");
            panel.appendChild(notice);
        } else if (this.manifestError) {
            const errorNotice = this.createNoticeElement(`Ошибка загрузки списка плагинов: ${this.manifestError}`, 'error');
            panel.appendChild(errorNotice);

            // Кнопка повторной загрузки
            const retryButton = document.createElement("button");
            retryButton.textContent = "Попробовать снова";
            retryButton.style.cssText = "padding: 8px 16px; margin: 10px 0; background: var(--brand-experiment); color: white; border: none; border-radius: 4px; cursor: pointer;";
            retryButton.onclick = async () => {
                await this.fetchManifestAndPrepareSettings();
                BdApi.showToast("Повторная загрузка завершена. Переоткройте настройки.", { type: "info" });
            };
            panel.appendChild(retryButton);
        } else if (!this.manifestLoaded || this.availablePlugins.length === 0) {
            const notice = this.createNoticeElement("Список доступных плагинов пуст или не загружен.", 'warn');
            panel.appendChild(notice);
        } else {
            // Создаем простые HTML переключатели
            for (const pluginInfo of this.availablePlugins) {
                const switchContainer = this.createSimpleSwitch(pluginInfo);
                panel.appendChild(switchContainer);
            }

            // Кнопка сохранения
            const saveButton = document.createElement("button");
            saveButton.textContent = "Сохранить настройки";
            saveButton.style.cssText = "padding: 10px 20px; margin-top: 20px; background: var(--brand-experiment); color: white; border: none; border-radius: 4px; cursor: pointer;";
            saveButton.onclick = () => {
                this.saveSettings();
                BdApi.showToast("Настройки сохранены!", { type: "success" });
            };
            panel.appendChild(saveButton);
        }

        return panel;
    }

    // Создание простого HTML переключателя
    createSimpleSwitch(pluginInfo) {
        const container = document.createElement("div");
        container.style.cssText = "display: flex; align-items: center; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid var(--background-modifier-accent);";

        const leftSide = document.createElement("div");
        leftSide.style.cssText = "flex: 1;";

        const name = document.createElement("div");
        name.textContent = pluginInfo.name;
        name.style.cssText = "font-weight: 500; color: var(--header-primary); margin-bottom: 4px;";

        const description = document.createElement("div");
        description.textContent = pluginInfo.description || `Включить/выключить плагин ${pluginInfo.name}`;
        description.style.cssText = "font-size: 14px; color: var(--header-secondary);";

        leftSide.appendChild(name);
        leftSide.appendChild(description);

        // Простой переключатель
        const switchElement = document.createElement("input");
        switchElement.type = "checkbox";
        switchElement.checked = this.pluginSettings[pluginInfo.name] === true;
        switchElement.style.cssText = "width: 20px; height: 20px; cursor: pointer;";
        switchElement.onchange = () => {
            this.pluginSettings[pluginInfo.name] = switchElement.checked;
        };

        container.appendChild(leftSide);
        container.appendChild(switchElement);

        return container;
    }

    createNoticeElement(text, type = 'info') {
         const notice = document.createElement("p");
         notice.textContent = text;
         notice.style.padding = "10px";
         notice.style.borderRadius = "5px";
         notice.style.marginBottom = "10px";
         switch(type) {
             case 'error':
                 notice.style.backgroundColor = "var(--background-danger)";
                 notice.style.border = "1px solid var(--status-danger)";
                 notice.style.color = "var(--text-normal)";
                 break;
             case 'warn':
                 notice.style.backgroundColor = "var(--background-warning)";
                 notice.style.border = "1px solid var(--status-warning)";
                 notice.style.color = "var(--text-normal)";
                 break;
             default:
                 notice.style.backgroundColor = "var(--background-secondary)";
                 notice.style.color = "var(--text-muted)";
         }
         return notice;
    }

    async loadPlugins() {
        if (!this.manifestLoaded || this.availablePlugins.length === 0) {
             console.log("MeldTeamLoader: Манифест не загружен или пуст. Загрузка плагинов невозможна.");
             return false;
        }

        const userId = this.getDiscordUserId();
        if (!userId) {
            BdApi.showToast("MeldTeam Plugins: Не удалось получить Discord ID для загрузки плагинов!", { type: "error" });
            return false;
        }

        console.log(`MeldTeamLoader: Проверка и загрузка выбранных плагинов...`);
        BdApi.showToast("MeldTeam Plugins: Загрузка плагинов...", { type: "info" });

        const pluginsToLoadNow = this.availablePlugins.filter(p => this.pluginSettings[p.name] === true);

        if (pluginsToLoadNow.length === 0) {
             console.log("MeldTeamLoader: Нет выбранных плагинов для загрузки.");
             BdApi.showToast("MeldTeam Plugins: Нет выбранных плагинов.", { type: "info" });
             return false;
        }

        console.log(`MeldTeamLoader: Планируется загрузка ${pluginsToLoadNow.length} плагинов:`, pluginsToLoadNow.map(p => p.name));

        window[this.pluginNamespace] = {};
        let successfulLoads = 0;

        for (const pluginInfo of pluginsToLoadNow) {
            const fetchUrl = `${this.pluginBaseUrl}?file=${pluginInfo.file}&_=${Date.now()}`;
            let isSuccess = false;

            try {
                console.log(`MeldTeamLoader: Загрузка ${pluginInfo.name} с ${fetchUrl}...`);
                const response = await fetch(fetchUrl, {
                    cache: "no-store",
                    headers: {
                         'X-Discord-User': userId
                    }
                });
                if (!response.ok) {
                     if (response.status === 403) {
                         throw new Error(`Доступ запрещен (ошибка ${response.status}) для ${pluginInfo.file}. Ваш ID (${userId}) в вайтлисте?`);
                     }
                     throw new Error(`HTTP ${response.status} для ${pluginInfo.file}`);
                }
                const scriptContent = await response.text();

                try {
                     const modifiedScriptContent = `
                        ${scriptContent}
                        // --- Добавлено загрузчиком MeldTeam ---
                        try {
                             if (typeof ${pluginInfo.name} === 'function') {
                                 if (!window.${this.pluginNamespace}) { window.${this.pluginNamespace} = {}; }
                                 window.${this.pluginNamespace}.${pluginInfo.name} = ${pluginInfo.name};
                                 console.log('MeldTeamLoader (eval): Класс ${pluginInfo.name} успешно зарегистрирован в ${this.pluginNamespace}.');
                             } else {
                                 console.error('MeldTeamLoader (eval): Класс ${pluginInfo.name} не был определен после выполнения кода.');
                             }
                        } catch (e) {
                             console.error('MeldTeamLoader (eval): Ошибка при попытке регистрации ${pluginInfo.name}:', e);
                        }
                        // --- Конец добавленного кода ---
                    `;
                     eval(modifiedScriptContent);
                     if (window[this.pluginNamespace] && typeof window[this.pluginNamespace][pluginInfo.name] === 'function') {
                        isSuccess = true;
                    } else {
                         isSuccess = false;
                    }
                } catch(e) {
                     console.error(`MeldTeamLoader: Ошибка при eval скрипта ${pluginInfo.name}:`, e);
                     isSuccess = false;
                }

                 if(isSuccess) successfulLoads++;
                 else BdApi.showToast(`MeldTeam Plugins: Ошибка регистрации ${pluginInfo.name}.`, { type: "error" });

            } catch (error) {
                console.error(`MeldTeamLoader: Не удалось загрузить или выполнить ${pluginInfo.name}:`, error);
                BdApi.showToast(`MeldTeam Plugins: Ошибка загрузки/eval ${pluginInfo.file}. ${error.message || ''}`, { type: "error", timeout: 5000 });
            }
        }

         const totalAttempted = pluginsToLoadNow.length;
         let message = `Загрузка завершена. Успешно загружено ${successfulLoads} из ${totalAttempted}.`;
         let type = "info";
         if (successfulLoads === totalAttempted && totalAttempted > 0) {
             message = `${totalAttempted} плагина(ов) успешно загружены.`;
             type = "success";
         } else if (successfulLoads > 0) {
              message = `Загружено ${successfulLoads} из ${totalAttempted} плагинов. Проверьте консоль.`;
              type = "warning";
         } else if (totalAttempted > 0) {
              message = `Не удалось загрузить ни один из ${totalAttempted} выбранных плагинов.`;
              type = "error";
         } else {
              return false;
         }
         BdApi.showToast(message, { type: type, timeout: (type === "success") ? 3000 : 5000 });

         return successfulLoads > 0;
    }

    startPlugins() {
        const loadedClasses = window[this.pluginNamespace];
        if (!loadedClasses || Object.keys(loadedClasses).length === 0) {
            console.log("MeldTeamLoader: Нет загруженных классов для запуска.");
            return;
        }

        console.log("MeldTeamLoader: Запуск загруженных плагинов...");
        let startedCount = 0;

        for (const className in loadedClasses) {
            if (loadedClasses.hasOwnProperty(className) && typeof loadedClasses[className] === 'function') {
                 if (this.pluginInstances.some(p => p.name === className)) {
                     console.warn(`MeldTeamLoader: Экземпляр ${className} уже существует. Пропуск запуска.`);
                     continue;
                 }

                    try {
                        console.log(`MeldTeamLoader: Создание экземпляра ${className}...`);
                        const instance = new loadedClasses[className]();
                     this.pluginInstances.push({ name: className, instance: instance });

                        if (typeof instance.start === 'function') {
                             console.log(`MeldTeamLoader: Запуск start() для ${className}...`);
                             instance.start();
                             console.log(`MeldTeamLoader: ${className} успешно запущен.`);
                         startedCount++;
                        } else {
                            console.warn(`MeldTeamLoader: У класса ${className} нет метода start().`);
                        }
                    } catch (error) {
                        console.error(`MeldTeamLoader: Ошибка при инициализации или запуске ${className}:`, error);
                        BdApi.showToast(`MeldTeam Plugins: Ошибка запуска ${className}.`, { type: "error" });
                      this.pluginInstances = this.pluginInstances.filter(p => p.name !== className);
                 }
            }
        }
        console.log(`MeldTeamLoader: Запуск завершен. Активно ${this.pluginInstances.length} плагинов.`);
    }
    stopPlugins() {
        console.log("MeldTeamLoader: Остановка активных плагинов...");
        if (this.pluginInstances.length === 0) {
            console.log("MeldTeamLoader: Нет активных плагинов для остановки.");
            return;
        }

        for (let i = this.pluginInstances.length - 1; i >= 0; i--) {
            const pluginData = this.pluginInstances[i];
            try {
                if (pluginData.instance && typeof pluginData.instance.stop === 'function') {
                    console.log(`MeldTeamLoader: Остановка ${pluginData.name}...`);
                    pluginData.instance.stop();
                }
            } catch (error) {
                console.error(`MeldTeamLoader: Ошибка при остановке ${pluginData.name}:`, error);
            }
        }
        const stoppedCount = this.pluginInstances.length;
        this.pluginInstances = [];
        console.log(`MeldTeamLoader: Остановлено ${stoppedCount} плагинов.`);

        if (window[this.pluginNamespace]) {
             delete window[this.pluginNamespace];
             console.log("MeldTeamLoader: Неймспейс плагинов очищен.");
        }
    }
    async reloadPlugins() {
         console.log("MeldTeamLoader: Перезагрузка плагинов из-за изменения настроек...");
         this.stopPlugins();
         await new Promise(resolve => setTimeout(resolve, 100));
         if (this.manifestLoaded) {
             const anyLoaded = await this.loadPlugins();
             if (anyLoaded) {
                 this.startPlugins();
             }
         } else {
             BdApi.showToast("MeldTeam Plugins: Манифест не загружен, перезагрузка невозможна.", { type: "warning" });
         }
         console.log("MeldTeamLoader: Перезагрузка завершена.");
    }

    getName() { return "MeldTeamPlugins"; }

    load() {
        this.pluginSettings = BdApi.loadData(this.getName(), "pluginSettings") || {};
        console.log("MeldTeamLoader: Предварительно загружены сохраненные настройки:", this.pluginSettings);

        // Улучшенная проверка ZLibrary
        const hasZLibrary = global.ZeresPluginLibrary || window.ZLibrary || global.ZLibrary;
        if (!hasZLibrary) {
            console.warn("MeldTeamLoader: ZeresPluginLibrary не найдена");
            BdApi.showToast(
                "MeldTeamPlugins: ZeresPluginLibrary не найдена. Будет использована упрощенная панель настроек.",
                { type: "warning", timeout: 7000 }
            );
        } else {
            console.log("MeldTeamLoader: ZeresPluginLibrary найдена");
        }

        this.getDiscordUserId();
    }

    async start() {
        console.log("MeldTeamLoader: Запуск основного загрузчика...");
        if (!this.getDiscordUserId()) {
             console.error("MeldTeamLoader: Не удалось получить Discord ID при старте. Плагин не может продолжить работу.");
             return;
        }

        await this.fetchManifestAndPrepareSettings();

        if (this.manifestLoaded) {
             const anyLoaded = await this.loadPlugins();
             if (anyLoaded) {
                 this.startPlugins();
             }
        } else {
             console.error("MeldTeamLoader: Не удалось загрузить манифест, запуск плагинов невозможен.");
        }
    }

    stop() {
        console.log("MeldTeamLoader: Остановка основного загрузчика...");
        this.stopPlugins();
        this.manifestLoaded = false;
        this.manifestLoading = false;
        this.manifestError = null;
        this.availablePlugins = [];
        this.discordUserId = null;
        BdApi.showToast("MeldTeam Plugins: Загрузчик остановлен.", { type: "info" });
    }
};

// --- ВАЖНО: НЕ УДАЛЯЙТЕ ЭТУ СТРОКУ ---
// Улучшенная проверка ZLibrary для совместимости
if (!global.ZeresPluginLibrary && !window.ZLibrary && !global.ZLibrary) {
    module.exports.prototype.load = () => BdApi.alert("Отсутствует ZeresPluginLibrary", "Для работы некоторых плагинов MeldTeam может требоваться библиотека ZeresPluginLibrary. Пожалуйста, скачайте и установите ее.");
}
// --- КОНЕЦ ВАЖНОЙ СТРОКИ ---
